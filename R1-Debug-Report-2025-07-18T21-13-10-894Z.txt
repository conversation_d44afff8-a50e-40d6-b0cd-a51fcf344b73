================================================================================
R1智能红外控制系统 - 调试报告
================================================================================
生成时间: 2025-07-18T21:13:10.886Z
会话时长: 47秒
日志总数: 682
用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
页面URL: http://***********:8000/

---------------------------------------- 系统状态 ----------------------------------------
{
  "currentModule": "signal-manager",
  "isInitialized": true,
  "modules": [
    "signalManager",
    "controlModule",
    "timerSettings",
    "statusDisplay",
    "systemMonitor"
  ],
  "performance": {
    "startTime": 115.40000000596046,
    "initTime": 100,
    "moduleLoadTime": 45.099999994039536,
    "memoryUsage": 0
  },
  "esp32Connected": true
}

---------------------------------------- 内存信息 ----------------------------------------
已用内存: 3MB
总内存: 4MB
内存限制: 4096MB

---------------------------------------- 错误统计 ----------------------------------------
LOG: 674条
ERROR: 8条

---------------------------------------- 详细日志 ----------------------------------------
[1] [+0ms] [LOG] 2025-07-18T21:12:23.591Z
✅ 错误收集器已启动，点击右下角按钮下载报告

[2] [+1ms] [LOG] 2025-07-18T21:12:23.592Z
✅ 错误收集器已启动，点击右下角按钮下载报告

[3] [+1ms] [ERROR] 2025-07-18T21:12:23.592Z
🚨 applySettings修复生效！esp32IP: ***********:8000 wsPort: 80

[4] [+2ms] [ERROR] 2025-07-18T21:12:23.593Z
🚨 applySettings修复生效！esp32IP: ***********:8000 wsPort: 80

[5] [+2ms] [ERROR] 2025-07-18T21:12:23.593Z
🚨 修复后的URL - HTTP: http://***********:8000 WebSocket: ws://***********:8001/ws

[6] [+2ms] [ERROR] 2025-07-18T21:12:23.593Z
🚨 修复后的URL - HTTP: http://***********:8000 WebSocket: ws://***********:8001/ws

[7] [+2ms] [LOG] 2025-07-18T21:12:23.593Z
🌙 深色模式已启用

[8] [+2ms] [LOG] 2025-07-18T21:12:23.593Z
🌙 深色模式已启用

[9] [+2ms] [LOG] 2025-07-18T21:12:23.593Z
📡 硬件配置已应用: {
  "irRecvPin": 14,
  "irSendPin": 21,
  "irFrequency": 38000,
  "irDutyCycle": 33,
  "statusLedPin": 2,
  "statusLedEnabled": true
}
  参数2: {
  "irRecvPin": 14,
  "irSendPin": 21,
  "irFrequency": 38000,
  "irDutyCycle": 33,
  "statusLedPin": 2,
  "statusLedEnabled": true
}

[10] [+2ms] [LOG] 2025-07-18T21:12:23.593Z
📡 硬件配置已应用: {
  "irRecvPin": 14,
  "irSendPin": 21,
  "irFrequency": 38000,
  "irDutyCycle": 33,
  "statusLedPin": 2,
  "statusLedEnabled": true
}
  参数2: {
  "irRecvPin": 14,
  "irSendPin": 21,
  "irFrequency": 38000,
  "irDutyCycle": 33,
  "statusLedPin": 2,
  "statusLedEnabled": true
}

[11] [+3ms] [LOG] 2025-07-18T21:12:23.594Z
✅ 核心组件初始化完成 (会话ID: session_73143591)

[12] [+3ms] [LOG] 2025-07-18T21:12:23.594Z
✅ 核心组件初始化完成 (会话ID: session_73143591)

[13] [+3ms] [LOG] 2025-07-18T21:12:23.594Z
➕ UnifiedTimerManager: 添加定时器 [system_time_update], 间隔: 1000ms, 重复: true, 下次执行: 2025/7/19 05:12:24

[14] [+3ms] [LOG] 2025-07-18T21:12:23.594Z
➕ UnifiedTimerManager: 添加定时器 [system_time_update], 间隔: 1000ms, 重复: true, 下次执行: 2025/7/19 05:12:24

[15] [+3ms] [LOG] 2025-07-18T21:12:23.594Z
🚀 UnifiedTimerManager: 主定时器已启动，间隔: 100ms, 定时器数量: 1

[16] [+3ms] [LOG] 2025-07-18T21:12:23.594Z
🚀 UnifiedTimerManager: 主定时器已启动，间隔: 100ms, 定时器数量: 1

[17] [+3ms] [LOG] 2025-07-18T21:12:23.594Z
⏰ 系统时间显示已启动

[18] [+3ms] [LOG] 2025-07-18T21:12:23.594Z
⏰ 系统时间显示已启动

[19] [+3ms] [LOG] 2025-07-18T21:12:23.594Z
📡 初始化ESP32管理器...

[20] [+3ms] [LOG] 2025-07-18T21:12:23.594Z
📡 初始化ESP32管理器...

[21] [+3ms] [LOG] 2025-07-18T21:12:23.594Z
🚀 初始化ESP32管理器...

[22] [+3ms] [LOG] 2025-07-18T21:12:23.594Z
🚀 初始化ESP32管理器...

[23] [+3ms] [LOG] 2025-07-18T21:12:23.594Z
📡 HTTP API: http://***********:8000

[24] [+3ms] [LOG] 2025-07-18T21:12:23.594Z
📡 HTTP API: http://***********:8000

[25] [+3ms] [LOG] 2025-07-18T21:12:23.594Z
🔌 WebSocket: ws://***********:8001/ws

[26] [+4ms] [LOG] 2025-07-18T21:12:23.595Z
🔌 WebSocket: ws://***********:8001/ws

[27] [+4ms] [LOG] 2025-07-18T21:12:23.595Z
🔍 测试后端连接...

[28] [+4ms] [LOG] 2025-07-18T21:12:23.595Z
🔍 测试后端连接...

[29] [+4ms] [LOG] 2025-07-18T21:12:23.595Z
📡 请求URL: http://***********:8000/api/status

[30] [+4ms] [LOG] 2025-07-18T21:12:23.595Z
📡 请求URL: http://***********:8000/api/status

[31] [+32ms] [LOG] 2025-07-18T21:12:23.623Z
📊 HTTP状态: 200 OK

[32] [+32ms] [LOG] 2025-07-18T21:12:23.623Z
📊 HTTP状态: 200 OK

[33] [+32ms] [LOG] 2025-07-18T21:12:23.623Z
📋 响应头: {
  "content-type": "application/json",
  "content-length": "237"
}
  参数2: {
  "content-type": "application/json",
  "content-length": "237"
}

[34] [+32ms] [LOG] 2025-07-18T21:12:23.623Z
📋 响应头: {
  "content-type": "application/json",
  "content-length": "237"
}
  参数2: {
  "content-type": "application/json",
  "content-length": "237"
}

[35] [+38ms] [LOG] 2025-07-18T21:12:23.629Z
📦 响应数据: {
  "success": true,
  "data": {
    "uptime": 75,
    "memory_usage": 35.21566,
    "signal_count": 0,
    "wifi_strength": 0,
    "free_heap": 224400,
    "chip_temperature": 49.1,
    "timestamp": 76397
  },
  "message": "系统状态获取成功",
  "timestamp": 1735689676398,
  "responseTime": 0
}
  参数2: {
  "success": true,
  "data": {
    "uptime": 75,
    "memory_usage": 35.21566,
    "signal_count": 0,
    "wifi_strength": 0,
    "free_heap": 224400,
    "chip_temperature": 49.1,
    "timestamp": 76397
  },
  "message": "系统状态获取成功",
  "timestamp": 1735689676398,
  "responseTime": 0
}

[36] [+38ms] [LOG] 2025-07-18T21:12:23.629Z
📦 响应数据: {
  "success": true,
  "data": {
    "uptime": 75,
    "memory_usage": 35.21566,
    "signal_count": 0,
    "wifi_strength": 0,
    "free_heap": 224400,
    "chip_temperature": 49.1,
    "timestamp": 76397
  },
  "message": "系统状态获取成功",
  "timestamp": 1735689676398,
  "responseTime": 0
}
  参数2: {
  "success": true,
  "data": {
    "uptime": 75,
    "memory_usage": 35.21566,
    "signal_count": 0,
    "wifi_strength": 0,
    "free_heap": 224400,
    "chip_temperature": 49.1,
    "timestamp": 76397
  },
  "message": "系统状态获取成功",
  "timestamp": 1735689676398,
  "responseTime": 0
}

[37] [+38ms] [LOG] 2025-07-18T21:12:23.629Z
✅ 后端连接正常

[38] [+38ms] [LOG] 2025-07-18T21:12:23.629Z
✅ 后端连接正常

[39] [+53ms] [LOG] 2025-07-18T21:12:23.644Z
✅ WebSocket连接成功

[40] [+53ms] [LOG] 2025-07-18T21:12:23.644Z
✅ WebSocket连接成功

[41] [+53ms] [LOG] 2025-07-18T21:12:23.644Z
✅ ESP32管理器初始化完成 - 在线模式

[42] [+53ms] [LOG] 2025-07-18T21:12:23.644Z
✅ ESP32管理器初始化完成 - 在线模式

[43] [+53ms] [LOG] 2025-07-18T21:12:23.644Z
✅ ESP32连接成功

[44] [+53ms] [LOG] 2025-07-18T21:12:23.644Z
✅ ESP32连接成功

[45] [+53ms] [LOG] 2025-07-18T21:12:23.644Z
📦 开始初始化所有模块...

[46] [+53ms] [LOG] 2025-07-18T21:12:23.644Z
📦 开始初始化所有模块...

[47] [+53ms] [LOG] 2025-07-18T21:12:23.644Z
🔧 初始化 signalManager 模块...

[48] [+53ms] [LOG] 2025-07-18T21:12:23.644Z
🔧 初始化 signalManager 模块...

[49] [+54ms] [LOG] 2025-07-18T21:12:23.645Z
🔍 signalManager 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[50] [+54ms] [LOG] 2025-07-18T21:12:23.645Z
🔍 signalManager 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[51] [+54ms] [LOG] 2025-07-18T21:12:23.645Z
🔧 开始初始化 SignalManager 模块...

[52] [+54ms] [LOG] 2025-07-18T21:12:23.645Z
🔧 开始初始化 SignalManager 模块...

[53] [+54ms] [LOG] 2025-07-18T21:12:23.645Z
🔧 初始化 controlModule 模块...

[54] [+54ms] [LOG] 2025-07-18T21:12:23.645Z
🔧 初始化 controlModule 模块...

[55] [+54ms] [LOG] 2025-07-18T21:12:23.645Z
🔍 controlModule 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[56] [+54ms] [LOG] 2025-07-18T21:12:23.645Z
🔍 controlModule 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[57] [+54ms] [LOG] 2025-07-18T21:12:23.645Z
🔧 开始初始化 ControlModule 模块...

[58] [+54ms] [LOG] 2025-07-18T21:12:23.645Z
🔧 开始初始化 ControlModule 模块...

[59] [+54ms] [LOG] 2025-07-18T21:12:23.645Z
🔧 初始化 timerSettings 模块...

[60] [+54ms] [LOG] 2025-07-18T21:12:23.645Z
🔧 初始化 timerSettings 模块...

[61] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
🔍 timerSettings 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[62] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
🔍 timerSettings 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[63] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
🔧 开始初始化 TimerSettings 模块...

[64] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
🔧 开始初始化 TimerSettings 模块...

[65] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
TimerSettings: 事件监听器设置完成

[66] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
TimerSettings: 事件监听器设置完成

[67] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
🔧 初始化 statusDisplay 模块...

[68] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
🔧 初始化 statusDisplay 模块...

[69] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
🔍 statusDisplay 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[70] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
🔍 statusDisplay 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[71] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
🔧 开始初始化 StatusDisplay 模块...

[72] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
🔧 开始初始化 StatusDisplay 模块...

[73] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
🔧 初始化 systemMonitor 模块...

[74] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
🔧 初始化 systemMonitor 模块...

[75] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
🔍 systemMonitor 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[76] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
🔍 systemMonitor 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[77] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
🔧 开始初始化 SystemMonitor 模块...

[78] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
🔧 开始初始化 SystemMonitor 模块...

[79] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
📡 SignalManager 事件监听器初始化完成

[80] [+55ms] [LOG] 2025-07-18T21:12:23.646Z
📡 SignalManager 事件监听器初始化完成

[81] [+56ms] [LOG] 2025-07-18T21:12:23.647Z
🔍 渲染信号 - 总数: 0, 过滤后: 0

[82] [+56ms] [LOG] 2025-07-18T21:12:23.647Z
🔍 渲染信号 - 总数: 0, 过滤后: 0

[83] [+56ms] [LOG] 2025-07-18T21:12:23.647Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[84] [+56ms] [LOG] 2025-07-18T21:12:23.647Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[85] [+56ms] [LOG] 2025-07-18T21:12:23.647Z
📡 ControlModule 事件监听器初始化完成

[86] [+56ms] [LOG] 2025-07-18T21:12:23.647Z
📡 ControlModule 事件监听器初始化完成

[87] [+56ms] [LOG] 2025-07-18T21:12:23.647Z
ControlModule: 事件绑定完成

[88] [+56ms] [LOG] 2025-07-18T21:12:23.647Z
ControlModule: 事件绑定完成

[89] [+56ms] [LOG] 2025-07-18T21:12:23.647Z
📡 TimerSettings 事件监听器初始化完成

[90] [+56ms] [LOG] 2025-07-18T21:12:23.647Z
📡 TimerSettings 事件监听器初始化完成

[91] [+57ms] [LOG] 2025-07-18T21:12:23.648Z
TimerSettings: 默认时间值设置完成（9-18点）

[92] [+57ms] [LOG] 2025-07-18T21:12:23.648Z
TimerSettings: 默认时间值设置完成（9-18点）

[93] [+57ms] [LOG] 2025-07-18T21:12:23.648Z
TimerSettings: 时间选择器事件绑定完成

[94] [+57ms] [LOG] 2025-07-18T21:12:23.648Z
TimerSettings: 时间选择器事件绑定完成

[95] [+57ms] [LOG] 2025-07-18T21:12:23.648Z
TimerSettings: 主开关状态: false

[96] [+57ms] [LOG] 2025-07-18T21:12:23.648Z
TimerSettings: 主开关状态: false

[97] [+57ms] [LOG] 2025-07-18T21:12:23.648Z
TimerSettings: 间隔设置状态已更新 {
  "hasMultipleTasks": false,
  "intervalDisabled": false,
  "reason": "单任务模式允许间隔"
}
  参数2: {
  "hasMultipleTasks": false,
  "intervalDisabled": false,
  "reason": "单任务模式允许间隔"
}

[98] [+57ms] [LOG] 2025-07-18T21:12:23.648Z
TimerSettings: 间隔设置状态已更新 {
  "hasMultipleTasks": false,
  "intervalDisabled": false,
  "reason": "单任务模式允许间隔"
}
  参数2: {
  "hasMultipleTasks": false,
  "intervalDisabled": false,
  "reason": "单任务模式允许间隔"
}

[99] [+57ms] [LOG] 2025-07-18T21:12:23.648Z
TimerSettings: 事件绑定完成

[100] [+57ms] [LOG] 2025-07-18T21:12:23.648Z
TimerSettings: 事件绑定完成

[101] [+57ms] [LOG] 2025-07-18T21:12:23.648Z
📡 StatusDisplay 事件监听器初始化完成

[102] [+57ms] [LOG] 2025-07-18T21:12:23.648Z
📡 StatusDisplay 事件监听器初始化完成

[103] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
📡 SystemMonitor 事件监听器初始化完成

[104] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
📡 SystemMonitor 事件监听器初始化完成

[105] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
🔍 SystemMonitor: 开始设置UI...

[106] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
🔍 SystemMonitor: 开始设置UI...

[107] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
🔍 SystemMonitor: 绑定过滤器事件...

[108] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
🔍 SystemMonitor: 绑定过滤器事件...

[109] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
🔍 SystemMonitor: 查找systemMonitorArea结果: {}
  参数2: {}

[110] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
🔍 SystemMonitor: 查找systemMonitorArea结果: {}
  参数2: {}

[111] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
🎨 SignalManager UI初始化完成

[112] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
🎨 SignalManager UI初始化完成

[113] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
📥 加载 0 个标准格式信号

[114] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
📥 加载 0 个标准格式信号

[115] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
✅ 从本地存储加载了 0 个信号

[116] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
✅ 从本地存储加载了 0 个信号

[117] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
📡 从ESP32加载信号列表...

[118] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
📡 从ESP32加载信号列表...

[119] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[120] [+58ms] [LOG] 2025-07-18T21:12:23.649Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[121] [+59ms] [LOG] 2025-07-18T21:12:23.650Z
🎨 ControlModule UI初始化完成

[122] [+59ms] [LOG] 2025-07-18T21:12:23.650Z
🎨 ControlModule UI初始化完成

[123] [+59ms] [LOG] 2025-07-18T21:12:23.650Z
🎨 TimerSettings UI初始化完成

[124] [+59ms] [LOG] 2025-07-18T21:12:23.650Z
🎨 TimerSettings UI初始化完成

[125] [+59ms] [LOG] 2025-07-18T21:12:23.650Z
TimerSettings: 数据加载待重新实现

[126] [+59ms] [LOG] 2025-07-18T21:12:23.650Z
TimerSettings: 数据加载待重新实现

[127] [+59ms] [LOG] 2025-07-18T21:12:23.650Z
🎨 StatusDisplay UI初始化完成

[128] [+59ms] [LOG] 2025-07-18T21:12:23.650Z
🎨 StatusDisplay UI初始化完成

[129] [+59ms] [LOG] 2025-07-18T21:12:23.650Z
StatusDisplay 数据加载完成，等待其他模块初始化

[130] [+59ms] [LOG] 2025-07-18T21:12:23.650Z
StatusDisplay 数据加载完成，等待其他模块初始化

[131] [+59ms] [LOG] 2025-07-18T21:12:23.650Z
🎨 SystemMonitor UI初始化完成

[132] [+59ms] [LOG] 2025-07-18T21:12:23.650Z
🎨 SystemMonitor UI初始化完成

[133] [+60ms] [LOG] 2025-07-18T21:12:23.651Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[134] [+60ms] [LOG] 2025-07-18T21:12:23.651Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[135] [+60ms] [LOG] 2025-07-18T21:12:23.651Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[136] [+60ms] [LOG] 2025-07-18T21:12:23.651Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[137] [+60ms] [LOG] 2025-07-18T21:12:23.651Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[138] [+60ms] [LOG] 2025-07-18T21:12:23.651Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[139] [+61ms] [LOG] 2025-07-18T21:12:23.652Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[140] [+61ms] [LOG] 2025-07-18T21:12:23.652Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[141] [+62ms] [LOG] 2025-07-18T21:12:23.653Z
📊 ControlModule 数据加载完成

[142] [+62ms] [LOG] 2025-07-18T21:12:23.653Z
📊 ControlModule 数据加载完成

[143] [+62ms] [LOG] 2025-07-18T21:12:23.653Z
✅ ControlModule 模块初始化完成，耗时: 7.30ms

[144] [+62ms] [LOG] 2025-07-18T21:12:23.653Z
✅ ControlModule 模块初始化完成，耗时: 7.30ms

[145] [+62ms] [LOG] 2025-07-18T21:12:23.653Z
📊 TimerSettings 数据加载完成

[146] [+62ms] [LOG] 2025-07-18T21:12:23.653Z
📊 TimerSettings 数据加载完成

[147] [+62ms] [LOG] 2025-07-18T21:12:23.653Z
✅ TimerSettings 模块初始化完成，耗时: 7.10ms

[148] [+62ms] [LOG] 2025-07-18T21:12:23.653Z
✅ TimerSettings 模块初始化完成，耗时: 7.10ms

[149] [+62ms] [LOG] 2025-07-18T21:12:23.653Z
📊 StatusDisplay 数据加载完成

[150] [+62ms] [LOG] 2025-07-18T21:12:23.653Z
📊 StatusDisplay 数据加载完成

[151] [+62ms] [LOG] 2025-07-18T21:12:23.653Z
✅ StatusDisplay 模块初始化完成，耗时: 6.80ms

[152] [+62ms] [LOG] 2025-07-18T21:12:23.653Z
✅ StatusDisplay 模块初始化完成，耗时: 6.80ms

[153] [+62ms] [LOG] 2025-07-18T21:12:23.653Z
📊 SystemMonitor 数据加载完成

[154] [+62ms] [LOG] 2025-07-18T21:12:23.653Z
📊 SystemMonitor 数据加载完成

[155] [+62ms] [LOG] 2025-07-18T21:12:23.653Z
✅ SystemMonitor 模块初始化完成，耗时: 6.80ms

[156] [+62ms] [LOG] 2025-07-18T21:12:23.653Z
✅ SystemMonitor 模块初始化完成，耗时: 6.80ms

[157] [+90ms] [LOG] 2025-07-18T21:12:23.681Z
✅ SignalManager: ESP32连接成功

[158] [+90ms] [LOG] 2025-07-18T21:12:23.681Z
✅ SignalManager: ESP32连接成功

[159] [+90ms] [LOG] 2025-07-18T21:12:23.681Z
📡 从ESP32加载信号列表...

[160] [+90ms] [LOG] 2025-07-18T21:12:23.681Z
📡 从ESP32加载信号列表...

[161] [+90ms] [LOG] 2025-07-18T21:12:23.681Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[162] [+90ms] [LOG] 2025-07-18T21:12:23.681Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[163] [+91ms] [LOG] 2025-07-18T21:12:23.682Z
✅ ControlModule: ESP32连接成功

[164] [+91ms] [LOG] 2025-07-18T21:12:23.682Z
✅ ControlModule: ESP32连接成功

[165] [+91ms] [LOG] 2025-07-18T21:12:23.682Z
✅ TimerSettings: ESP32连接成功

[166] [+91ms] [LOG] 2025-07-18T21:12:23.682Z
✅ TimerSettings: ESP32连接成功

[167] [+91ms] [LOG] 2025-07-18T21:12:23.682Z
📡 从ESP32加载定时任务列表...

[168] [+91ms] [LOG] 2025-07-18T21:12:23.682Z
📡 从ESP32加载定时任务列表...

[169] [+91ms] [LOG] 2025-07-18T21:12:23.682Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/timer/tasks",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/timer/tasks",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[170] [+91ms] [LOG] 2025-07-18T21:12:23.682Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/timer/tasks",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/timer/tasks",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[171] [+94ms] [LOG] 2025-07-18T21:12:23.685Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[172] [+94ms] [LOG] 2025-07-18T21:12:23.685Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[173] [+94ms] [LOG] 2025-07-18T21:12:23.685Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[174] [+94ms] [LOG] 2025-07-18T21:12:23.685Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[175] [+94ms] [LOG] 2025-07-18T21:12:23.685Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[176] [+94ms] [LOG] 2025-07-18T21:12:23.685Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[177] [+95ms] [LOG] 2025-07-18T21:12:23.686Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[178] [+95ms] [LOG] 2025-07-18T21:12:23.686Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[179] [+96ms] [LOG] 2025-07-18T21:12:23.687Z
✅ 模块就绪: ControlModule

[180] [+96ms] [LOG] 2025-07-18T21:12:23.687Z
✅ 模块就绪: ControlModule

[181] [+96ms] [LOG] 2025-07-18T21:12:23.687Z
✅ 模块就绪: TimerSettings

[182] [+96ms] [LOG] 2025-07-18T21:12:23.687Z
✅ 模块就绪: TimerSettings

[183] [+96ms] [LOG] 2025-07-18T21:12:23.687Z
✅ 模块就绪: StatusDisplay

[184] [+96ms] [LOG] 2025-07-18T21:12:23.687Z
✅ 模块就绪: StatusDisplay

[185] [+96ms] [LOG] 2025-07-18T21:12:23.687Z
✅ 模块就绪: SystemMonitor

[186] [+96ms] [LOG] 2025-07-18T21:12:23.687Z
✅ 模块就绪: SystemMonitor

[187] [+97ms] [LOG] 2025-07-18T21:12:23.688Z
💾 保存 0 个标准格式信号

[188] [+97ms] [LOG] 2025-07-18T21:12:23.688Z
💾 保存 0 个标准格式信号

[189] [+97ms] [LOG] 2025-07-18T21:12:23.688Z
💾 保存前信号总数: 0

[190] [+97ms] [LOG] 2025-07-18T21:12:23.688Z
💾 保存前信号总数: 0

[191] [+97ms] [LOG] 2025-07-18T21:12:23.688Z
💾 准备保存 0 个有效信号

[192] [+97ms] [LOG] 2025-07-18T21:12:23.688Z
💾 准备保存 0 个有效信号

[193] [+97ms] [LOG] 2025-07-18T21:12:23.688Z
💾 内存中保留 0 个信号

[194] [+97ms] [LOG] 2025-07-18T21:12:23.688Z
💾 内存中保留 0 个信号

[195] [+97ms] [LOG] 2025-07-18T21:12:23.688Z
✅ 标准格式信号数据保存完成 - 保存了 0 个信号

[196] [+97ms] [LOG] 2025-07-18T21:12:23.688Z
✅ 标准格式信号数据保存完成 - 保存了 0 个信号

[197] [+97ms] [LOG] 2025-07-18T21:12:23.688Z
🔍 渲染信号 - 总数: 0, 过滤后: 0

[198] [+97ms] [LOG] 2025-07-18T21:12:23.688Z
🔍 渲染信号 - 总数: 0, 过滤后: 0

[199] [+97ms] [LOG] 2025-07-18T21:12:23.688Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[200] [+97ms] [LOG] 2025-07-18T21:12:23.688Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[201] [+97ms] [LOG] 2025-07-18T21:12:23.688Z
🔍 使用缓存，跳过渲染

[202] [+97ms] [LOG] 2025-07-18T21:12:23.688Z
🔍 使用缓存，跳过渲染

[203] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
✅ 从ESP32加载了 0 个信号

[204] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
✅ 从ESP32加载了 0 个信号

[205] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
🔍 渲染信号 - 总数: 0, 过滤后: 0

[206] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
🔍 渲染信号 - 总数: 0, 过滤后: 0

[207] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[208] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[209] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
🔍 使用缓存，跳过渲染

[210] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
🔍 使用缓存，跳过渲染

[211] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
📊 SignalManager 数据加载完成

[212] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
📊 SignalManager 数据加载完成

[213] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
✅ SignalManager 模块初始化完成，耗时: 44.00ms

[214] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
✅ SignalManager 模块初始化完成，耗时: 44.00ms

[215] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
✅ signalManager 模块初始化成功

[216] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
✅ signalManager 模块初始化成功

[217] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
✅ controlModule 模块初始化成功

[218] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
✅ controlModule 模块初始化成功

[219] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
✅ timerSettings 模块初始化成功

[220] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
✅ timerSettings 模块初始化成功

[221] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
✅ statusDisplay 模块初始化成功

[222] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
✅ statusDisplay 模块初始化成功

[223] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
✅ systemMonitor 模块初始化成功

[224] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
✅ systemMonitor 模块初始化成功

[225] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
📦 模块初始化完成: 成功 5 个，失败 0 个，耗时: 45.10ms

[226] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
📦 模块初始化完成: 成功 5 个，失败 0 个，耗时: 45.10ms

[227] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
✅ R1系统启动完成，耗时: 100.00ms

[228] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
✅ R1系统启动完成，耗时: 100.00ms

[229] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
➕ UnifiedTimerManager: 添加定时器 [notification_notification_73143689], 间隔: 5000ms, 重复: false, 下次执行: 2025/7/19 05:12:28

[230] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
➕ UnifiedTimerManager: 添加定时器 [notification_notification_73143689], 间隔: 5000ms, 重复: false, 下次执行: 2025/7/19 05:12:28

[231] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
⚠️ UnifiedTimerManager: 主定时器已在运行

[232] [+98ms] [LOG] 2025-07-18T21:12:23.689Z
⚠️ UnifiedTimerManager: 主定时器已在运行

[233] [+115ms] [LOG] 2025-07-18T21:12:23.706Z
✅ 模块就绪: SignalManager

[234] [+115ms] [LOG] 2025-07-18T21:12:23.706Z
✅ 模块就绪: SignalManager

[235] [+115ms] [LOG] 2025-07-18T21:12:23.706Z
🎉 所有模块初始化完成: 成功 5 个

[236] [+115ms] [LOG] 2025-07-18T21:12:23.706Z
🎉 所有模块初始化完成: 成功 5 个

[237] [+115ms] [LOG] 2025-07-18T21:12:23.706Z
StatusDisplay: 收到系统模块初始化完成事件，开始自动更新

[238] [+115ms] [LOG] 2025-07-18T21:12:23.706Z
StatusDisplay: 收到系统模块初始化完成事件，开始自动更新

[239] [+115ms] [LOG] 2025-07-18T21:12:23.706Z
StatusDisplay: updateCurrentTaskDisplay 被调用，现在主要通过事件驱动更新

[240] [+115ms] [LOG] 2025-07-18T21:12:23.706Z
StatusDisplay: updateCurrentTaskDisplay 被调用，现在主要通过事件驱动更新

[241] [+404ms] [LOG] 2025-07-18T21:12:23.995Z
📨 WebSocket消息: {
  "type": "connected",
  "payload": {
    "message": "WebSocket连接成功",
    "clientId": "client_3",
    "serverTime": 76782,
    "serverVersion": "1.0.0"
  },
  "timestamp": 76782
}
  参数2: {
  "type": "connected",
  "payload": {
    "message": "WebSocket连接成功",
    "clientId": "client_3",
    "serverTime": 76782,
    "serverVersion": "1.0.0"
  },
  "timestamp": 76782
}

[242] [+405ms] [LOG] 2025-07-18T21:12:23.996Z
📨 WebSocket消息: {
  "type": "connected",
  "payload": {
    "message": "WebSocket连接成功",
    "clientId": "client_3",
    "serverTime": 76782,
    "serverVersion": "1.0.0"
  },
  "timestamp": 76782
}
  参数2: {
  "type": "connected",
  "payload": {
    "message": "WebSocket连接成功",
    "clientId": "client_3",
    "serverTime": 76782,
    "serverVersion": "1.0.0"
  },
  "timestamp": 76782
}

[243] [+407ms] [LOG] 2025-07-18T21:12:23.998Z
💾 保存 0 个标准格式信号

[244] [+407ms] [LOG] 2025-07-18T21:12:23.998Z
💾 保存 0 个标准格式信号

[245] [+407ms] [LOG] 2025-07-18T21:12:23.998Z
💾 保存前信号总数: 0

[246] [+408ms] [LOG] 2025-07-18T21:12:23.999Z
💾 保存前信号总数: 0

[247] [+408ms] [LOG] 2025-07-18T21:12:23.999Z
💾 准备保存 0 个有效信号

[248] [+408ms] [LOG] 2025-07-18T21:12:23.999Z
💾 准备保存 0 个有效信号

[249] [+409ms] [LOG] 2025-07-18T21:12:24.000Z
💾 内存中保留 0 个信号

[250] [+409ms] [LOG] 2025-07-18T21:12:24.000Z
💾 内存中保留 0 个信号

[251] [+409ms] [LOG] 2025-07-18T21:12:24.000Z
✅ 标准格式信号数据保存完成 - 保存了 0 个信号

[252] [+409ms] [LOG] 2025-07-18T21:12:24.000Z
✅ 标准格式信号数据保存完成 - 保存了 0 个信号

[253] [+409ms] [LOG] 2025-07-18T21:12:24.000Z
🔍 渲染信号 - 总数: 0, 过滤后: 0

[254] [+410ms] [LOG] 2025-07-18T21:12:24.001Z
🔍 渲染信号 - 总数: 0, 过滤后: 0

[255] [+410ms] [LOG] 2025-07-18T21:12:24.001Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[256] [+410ms] [LOG] 2025-07-18T21:12:24.001Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[257] [+410ms] [LOG] 2025-07-18T21:12:24.001Z
🔍 使用缓存，跳过渲染

[258] [+410ms] [LOG] 2025-07-18T21:12:24.001Z
🔍 使用缓存，跳过渲染

[259] [+411ms] [LOG] 2025-07-18T21:12:24.002Z
✅ 从ESP32加载了 0 个信号

[260] [+411ms] [LOG] 2025-07-18T21:12:24.002Z
✅ 从ESP32加载了 0 个信号

[261] [+418ms] [LOG] 2025-07-18T21:12:24.009Z
✅ SignalManager: ESP32连接成功

[262] [+418ms] [LOG] 2025-07-18T21:12:24.009Z
✅ SignalManager: ESP32连接成功

[263] [+419ms] [LOG] 2025-07-18T21:12:24.010Z
📡 从ESP32加载信号列表...

[264] [+419ms] [LOG] 2025-07-18T21:12:24.010Z
📡 从ESP32加载信号列表...

[265] [+419ms] [LOG] 2025-07-18T21:12:24.010Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[266] [+419ms] [LOG] 2025-07-18T21:12:24.010Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[267] [+420ms] [LOG] 2025-07-18T21:12:24.011Z
✅ ControlModule: ESP32连接成功

[268] [+420ms] [LOG] 2025-07-18T21:12:24.011Z
✅ ControlModule: ESP32连接成功

[269] [+420ms] [LOG] 2025-07-18T21:12:24.011Z
✅ TimerSettings: ESP32连接成功

[270] [+420ms] [LOG] 2025-07-18T21:12:24.011Z
✅ TimerSettings: ESP32连接成功

[271] [+420ms] [LOG] 2025-07-18T21:12:24.011Z
📡 从ESP32加载定时任务列表...

[272] [+420ms] [LOG] 2025-07-18T21:12:24.011Z
📡 从ESP32加载定时任务列表...

[273] [+421ms] [LOG] 2025-07-18T21:12:24.012Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/timer/tasks",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/timer/tasks",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[274] [+421ms] [LOG] 2025-07-18T21:12:24.012Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/timer/tasks",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/timer/tasks",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[275] [+425ms] [LOG] 2025-07-18T21:12:24.016Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[276] [+425ms] [LOG] 2025-07-18T21:12:24.016Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[277] [+425ms] [LOG] 2025-07-18T21:12:24.016Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[278] [+425ms] [LOG] 2025-07-18T21:12:24.016Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[279] [+427ms] [LOG] 2025-07-18T21:12:24.018Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[280] [+427ms] [LOG] 2025-07-18T21:12:24.018Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[281] [+428ms] [LOG] 2025-07-18T21:12:24.019Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[282] [+428ms] [LOG] 2025-07-18T21:12:24.019Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[283] [+430ms] [ERROR] 2025-07-18T21:12:24.021Z
❌ 从ESP32加载定时任务失败: {}
  参数2: {}

[284] [+430ms] [ERROR] 2025-07-18T21:12:24.021Z
❌ 从ESP32加载定时任务失败: {}
  参数2: {}

[285] [+733ms] [LOG] 2025-07-18T21:12:24.324Z
💾 保存 0 个标准格式信号

[286] [+733ms] [LOG] 2025-07-18T21:12:24.324Z
💾 保存 0 个标准格式信号

[287] [+733ms] [LOG] 2025-07-18T21:12:24.324Z
💾 保存前信号总数: 0

[288] [+734ms] [LOG] 2025-07-18T21:12:24.325Z
💾 保存前信号总数: 0

[289] [+734ms] [LOG] 2025-07-18T21:12:24.325Z
💾 准备保存 0 个有效信号

[290] [+735ms] [LOG] 2025-07-18T21:12:24.326Z
💾 准备保存 0 个有效信号

[291] [+735ms] [LOG] 2025-07-18T21:12:24.326Z
💾 内存中保留 0 个信号

[292] [+735ms] [LOG] 2025-07-18T21:12:24.326Z
💾 内存中保留 0 个信号

[293] [+736ms] [LOG] 2025-07-18T21:12:24.327Z
✅ 标准格式信号数据保存完成 - 保存了 0 个信号

[294] [+736ms] [LOG] 2025-07-18T21:12:24.327Z
✅ 标准格式信号数据保存完成 - 保存了 0 个信号

[295] [+736ms] [LOG] 2025-07-18T21:12:24.327Z
🔍 渲染信号 - 总数: 0, 过滤后: 0

[296] [+736ms] [LOG] 2025-07-18T21:12:24.327Z
🔍 渲染信号 - 总数: 0, 过滤后: 0

[297] [+737ms] [LOG] 2025-07-18T21:12:24.328Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[298] [+737ms] [LOG] 2025-07-18T21:12:24.328Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[299] [+737ms] [LOG] 2025-07-18T21:12:24.328Z
🔍 使用缓存，跳过渲染

[300] [+737ms] [LOG] 2025-07-18T21:12:24.328Z
🔍 使用缓存，跳过渲染

[301] [+737ms] [LOG] 2025-07-18T21:12:24.328Z
✅ 从ESP32加载了 0 个信号

[302] [+737ms] [LOG] 2025-07-18T21:12:24.328Z
✅ 从ESP32加载了 0 个信号

[303] [+741ms] [ERROR] 2025-07-18T21:12:24.332Z
❌ 从ESP32加载定时任务失败: {}
  参数2: {}

[304] [+741ms] [ERROR] 2025-07-18T21:12:24.332Z
❌ 从ESP32加载定时任务失败: {}
  参数2: {}

[305] [+741ms] [LOG] 2025-07-18T21:12:24.332Z
📨 WebSocket消息: {
  "type": "websocket_client_connected",
  "payload": {
    "client_id": 3,
    "client_ip": "***********",
    "total_clients": 1
  },
  "timestamp": 1735689676784
}
  参数2: {
  "type": "websocket_client_connected",
  "payload": {
    "client_id": 3,
    "client_ip": "***********",
    "total_clients": 1
  },
  "timestamp": 1735689676784
}

[306] [+742ms] [LOG] 2025-07-18T21:12:24.333Z
📨 WebSocket消息: {
  "type": "websocket_client_connected",
  "payload": {
    "client_id": 3,
    "client_ip": "***********",
    "total_clients": 1
  },
  "timestamp": 1735689676784
}
  参数2: {
  "type": "websocket_client_connected",
  "payload": {
    "client_id": 3,
    "client_ip": "***********",
    "total_clients": 1
  },
  "timestamp": 1735689676784
}

[307] [+1018ms] [LOG] 2025-07-18T21:12:24.609Z
⏰ UnifiedTimerManager: 1 个定时器到期

[308] [+1018ms] [LOG] 2025-07-18T21:12:24.609Z
⏰ UnifiedTimerManager: 1 个定时器到期

[309] [+1019ms] [LOG] 2025-07-18T21:12:24.610Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 14ms

[310] [+1019ms] [LOG] 2025-07-18T21:12:24.610Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 14ms

[311] [+1020ms] [LOG] 2025-07-18T21:12:24.611Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[312] [+1020ms] [LOG] 2025-07-18T21:12:24.611Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[313] [+1020ms] [LOG] 2025-07-18T21:12:24.611Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:25

[314] [+1021ms] [LOG] 2025-07-18T21:12:24.612Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:25

[315] [+2109ms] [LOG] 2025-07-18T21:12:25.700Z
⏰ UnifiedTimerManager: 1 个定时器到期

[316] [+2109ms] [LOG] 2025-07-18T21:12:25.700Z
⏰ UnifiedTimerManager: 1 个定时器到期

[317] [+2109ms] [LOG] 2025-07-18T21:12:25.700Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[318] [+2109ms] [LOG] 2025-07-18T21:12:25.700Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[319] [+2109ms] [LOG] 2025-07-18T21:12:25.700Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[320] [+2109ms] [LOG] 2025-07-18T21:12:25.700Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[321] [+2109ms] [LOG] 2025-07-18T21:12:25.700Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:26

[322] [+2109ms] [LOG] 2025-07-18T21:12:25.700Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:26

[323] [+3116ms] [LOG] 2025-07-18T21:12:26.707Z
⏰ UnifiedTimerManager: 1 个定时器到期

[324] [+3116ms] [LOG] 2025-07-18T21:12:26.707Z
⏰ UnifiedTimerManager: 1 个定时器到期

[325] [+3117ms] [LOG] 2025-07-18T21:12:26.708Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 8ms

[326] [+3117ms] [LOG] 2025-07-18T21:12:26.708Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 8ms

[327] [+3117ms] [LOG] 2025-07-18T21:12:26.708Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[328] [+3117ms] [LOG] 2025-07-18T21:12:26.708Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[329] [+3117ms] [LOG] 2025-07-18T21:12:26.708Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:27

[330] [+3117ms] [LOG] 2025-07-18T21:12:26.708Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:27

[331] [+4215ms] [LOG] 2025-07-18T21:12:27.806Z
⏰ UnifiedTimerManager: 1 个定时器到期

[332] [+4215ms] [LOG] 2025-07-18T21:12:27.806Z
⏰ UnifiedTimerManager: 1 个定时器到期

[333] [+4215ms] [LOG] 2025-07-18T21:12:27.806Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[334] [+4215ms] [LOG] 2025-07-18T21:12:27.806Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[335] [+4216ms] [LOG] 2025-07-18T21:12:27.807Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[336] [+4216ms] [LOG] 2025-07-18T21:12:27.807Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[337] [+4216ms] [LOG] 2025-07-18T21:12:27.807Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:28

[338] [+4216ms] [LOG] 2025-07-18T21:12:27.807Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:28

[339] [+5105ms] [LOG] 2025-07-18T21:12:28.696Z
⏰ UnifiedTimerManager: 1 个定时器到期

[340] [+5106ms] [LOG] 2025-07-18T21:12:28.697Z
⏰ UnifiedTimerManager: 1 个定时器到期

[341] [+5106ms] [LOG] 2025-07-18T21:12:28.697Z
🔥 UnifiedTimerManager: 执行定时器 [notification_notification_73143689], 延迟: 7ms

[342] [+5106ms] [LOG] 2025-07-18T21:12:28.697Z
🔥 UnifiedTimerManager: 执行定时器 [notification_notification_73143689], 延迟: 7ms

[343] [+5106ms] [LOG] 2025-07-18T21:12:28.697Z
➕ UnifiedTimerManager: 添加定时器 [notification_remove_notification_73143689], 间隔: 300ms, 重复: false, 下次执行: 2025/7/19 05:12:28

[344] [+5106ms] [LOG] 2025-07-18T21:12:28.697Z
➕ UnifiedTimerManager: 添加定时器 [notification_remove_notification_73143689], 间隔: 300ms, 重复: false, 下次执行: 2025/7/19 05:12:28

[345] [+5106ms] [LOG] 2025-07-18T21:12:28.697Z
⚠️ UnifiedTimerManager: 主定时器已在运行

[346] [+5106ms] [LOG] 2025-07-18T21:12:28.697Z
⚠️ UnifiedTimerManager: 主定时器已在运行

[347] [+5106ms] [LOG] 2025-07-18T21:12:28.697Z
✅ UnifiedTimerManager: 定时器 [notification_notification_73143689] 执行成功

[348] [+5106ms] [LOG] 2025-07-18T21:12:28.697Z
✅ UnifiedTimerManager: 定时器 [notification_notification_73143689] 执行成功

[349] [+5106ms] [LOG] 2025-07-18T21:12:28.697Z
🗑️ UnifiedTimerManager: 一次性定时器 [notification_notification_73143689] 执行完成，已删除

[350] [+5106ms] [LOG] 2025-07-18T21:12:28.697Z
🗑️ UnifiedTimerManager: 一次性定时器 [notification_notification_73143689] 执行完成，已删除

[351] [+5306ms] [LOG] 2025-07-18T21:12:28.897Z
⏰ UnifiedTimerManager: 1 个定时器到期

[352] [+5306ms] [LOG] 2025-07-18T21:12:28.897Z
⏰ UnifiedTimerManager: 1 个定时器到期

[353] [+5306ms] [LOG] 2025-07-18T21:12:28.897Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[354] [+5306ms] [LOG] 2025-07-18T21:12:28.897Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[355] [+5307ms] [LOG] 2025-07-18T21:12:28.898Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[356] [+5307ms] [LOG] 2025-07-18T21:12:28.898Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[357] [+5307ms] [LOG] 2025-07-18T21:12:28.898Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:29

[358] [+5307ms] [LOG] 2025-07-18T21:12:28.898Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:29

[359] [+5415ms] [LOG] 2025-07-18T21:12:29.006Z
⏰ UnifiedTimerManager: 1 个定时器到期

[360] [+5415ms] [LOG] 2025-07-18T21:12:29.006Z
⏰ UnifiedTimerManager: 1 个定时器到期

[361] [+5415ms] [LOG] 2025-07-18T21:12:29.006Z
🔥 UnifiedTimerManager: 执行定时器 [notification_remove_notification_73143689], 延迟: 9ms

[362] [+5415ms] [LOG] 2025-07-18T21:12:29.006Z
🔥 UnifiedTimerManager: 执行定时器 [notification_remove_notification_73143689], 延迟: 9ms

[363] [+5415ms] [LOG] 2025-07-18T21:12:29.006Z
✅ UnifiedTimerManager: 定时器 [notification_remove_notification_73143689] 执行成功

[364] [+5415ms] [LOG] 2025-07-18T21:12:29.006Z
✅ UnifiedTimerManager: 定时器 [notification_remove_notification_73143689] 执行成功

[365] [+5415ms] [LOG] 2025-07-18T21:12:29.006Z
🗑️ UnifiedTimerManager: 一次性定时器 [notification_remove_notification_73143689] 执行完成，已删除

[366] [+5415ms] [LOG] 2025-07-18T21:12:29.006Z
🗑️ UnifiedTimerManager: 一次性定时器 [notification_remove_notification_73143689] 执行完成，已删除

[367] [+6315ms] [LOG] 2025-07-18T21:12:29.906Z
⏰ UnifiedTimerManager: 1 个定时器到期

[368] [+6315ms] [LOG] 2025-07-18T21:12:29.906Z
⏰ UnifiedTimerManager: 1 个定时器到期

[369] [+6315ms] [LOG] 2025-07-18T21:12:29.906Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 9ms

[370] [+6315ms] [LOG] 2025-07-18T21:12:29.906Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 9ms

[371] [+6315ms] [LOG] 2025-07-18T21:12:29.906Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[372] [+6315ms] [LOG] 2025-07-18T21:12:29.906Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[373] [+6315ms] [LOG] 2025-07-18T21:12:29.906Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:30

[374] [+6315ms] [LOG] 2025-07-18T21:12:29.906Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:30

[375] [+7407ms] [LOG] 2025-07-18T21:12:30.998Z
⏰ UnifiedTimerManager: 1 个定时器到期

[376] [+7407ms] [LOG] 2025-07-18T21:12:30.998Z
⏰ UnifiedTimerManager: 1 个定时器到期

[377] [+7407ms] [LOG] 2025-07-18T21:12:30.998Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[378] [+7407ms] [LOG] 2025-07-18T21:12:30.998Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[379] [+7408ms] [LOG] 2025-07-18T21:12:30.999Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[380] [+7408ms] [LOG] 2025-07-18T21:12:30.999Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[381] [+7409ms] [LOG] 2025-07-18T21:12:31.000Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:31

[382] [+7409ms] [LOG] 2025-07-18T21:12:31.000Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:31

[383] [+8512ms] [LOG] 2025-07-18T21:12:32.103Z
⏰ UnifiedTimerManager: 1 个定时器到期

[384] [+8512ms] [LOG] 2025-07-18T21:12:32.103Z
⏰ UnifiedTimerManager: 1 个定时器到期

[385] [+8512ms] [LOG] 2025-07-18T21:12:32.103Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 106ms

[386] [+8512ms] [LOG] 2025-07-18T21:12:32.103Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 106ms

[387] [+8512ms] [LOG] 2025-07-18T21:12:32.103Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[388] [+8512ms] [LOG] 2025-07-18T21:12:32.103Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[389] [+8512ms] [LOG] 2025-07-18T21:12:32.103Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:33

[390] [+8512ms] [LOG] 2025-07-18T21:12:32.103Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:33

[391] [+9613ms] [LOG] 2025-07-18T21:12:33.204Z
⏰ UnifiedTimerManager: 1 个定时器到期

[392] [+9613ms] [LOG] 2025-07-18T21:12:33.204Z
⏰ UnifiedTimerManager: 1 个定时器到期

[393] [+9613ms] [LOG] 2025-07-18T21:12:33.204Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 101ms

[394] [+9613ms] [LOG] 2025-07-18T21:12:33.204Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 101ms

[395] [+9613ms] [LOG] 2025-07-18T21:12:33.204Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[396] [+9613ms] [LOG] 2025-07-18T21:12:33.204Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[397] [+9614ms] [LOG] 2025-07-18T21:12:33.205Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:34

[398] [+9614ms] [LOG] 2025-07-18T21:12:33.205Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:34

[399] [+10704ms] [LOG] 2025-07-18T21:12:34.295Z
⏰ UnifiedTimerManager: 1 个定时器到期

[400] [+10704ms] [LOG] 2025-07-18T21:12:34.295Z
⏰ UnifiedTimerManager: 1 个定时器到期

[401] [+10705ms] [LOG] 2025-07-18T21:12:34.296Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[402] [+10705ms] [LOG] 2025-07-18T21:12:34.296Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[403] [+10705ms] [LOG] 2025-07-18T21:12:34.296Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[404] [+10705ms] [LOG] 2025-07-18T21:12:34.296Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[405] [+10705ms] [LOG] 2025-07-18T21:12:34.296Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:35

[406] [+10705ms] [LOG] 2025-07-18T21:12:34.296Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:35

[407] [+11712ms] [LOG] 2025-07-18T21:12:35.303Z
⏰ UnifiedTimerManager: 1 个定时器到期

[408] [+11712ms] [LOG] 2025-07-18T21:12:35.303Z
⏰ UnifiedTimerManager: 1 个定时器到期

[409] [+11712ms] [LOG] 2025-07-18T21:12:35.303Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 8ms

[410] [+11712ms] [LOG] 2025-07-18T21:12:35.303Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 8ms

[411] [+11713ms] [LOG] 2025-07-18T21:12:35.304Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[412] [+11713ms] [LOG] 2025-07-18T21:12:35.304Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[413] [+11713ms] [LOG] 2025-07-18T21:12:35.304Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:36

[414] [+11713ms] [LOG] 2025-07-18T21:12:35.304Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:36

[415] [+12713ms] [LOG] 2025-07-18T21:12:36.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[416] [+12713ms] [LOG] 2025-07-18T21:12:36.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[417] [+12713ms] [LOG] 2025-07-18T21:12:36.304Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[418] [+12713ms] [LOG] 2025-07-18T21:12:36.304Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[419] [+12713ms] [LOG] 2025-07-18T21:12:36.304Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[420] [+12714ms] [LOG] 2025-07-18T21:12:36.305Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[421] [+12714ms] [LOG] 2025-07-18T21:12:36.305Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:37

[422] [+12714ms] [LOG] 2025-07-18T21:12:36.305Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:37

[423] [+13804ms] [LOG] 2025-07-18T21:12:37.395Z
⏰ UnifiedTimerManager: 1 个定时器到期

[424] [+13804ms] [LOG] 2025-07-18T21:12:37.395Z
⏰ UnifiedTimerManager: 1 个定时器到期

[425] [+13804ms] [LOG] 2025-07-18T21:12:37.395Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[426] [+13804ms] [LOG] 2025-07-18T21:12:37.395Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[427] [+13804ms] [LOG] 2025-07-18T21:12:37.395Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[428] [+13804ms] [LOG] 2025-07-18T21:12:37.395Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[429] [+13805ms] [LOG] 2025-07-18T21:12:37.396Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:38

[430] [+13805ms] [LOG] 2025-07-18T21:12:37.396Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:38

[431] [+14811ms] [LOG] 2025-07-18T21:12:38.402Z
⏰ UnifiedTimerManager: 1 个定时器到期

[432] [+14811ms] [LOG] 2025-07-18T21:12:38.402Z
⏰ UnifiedTimerManager: 1 个定时器到期

[433] [+14812ms] [LOG] 2025-07-18T21:12:38.403Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 8ms

[434] [+14812ms] [LOG] 2025-07-18T21:12:38.403Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 8ms

[435] [+14812ms] [LOG] 2025-07-18T21:12:38.403Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[436] [+14812ms] [LOG] 2025-07-18T21:12:38.403Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[437] [+14812ms] [LOG] 2025-07-18T21:12:38.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:39

[438] [+14812ms] [LOG] 2025-07-18T21:12:38.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:39

[439] [+15910ms] [LOG] 2025-07-18T21:12:39.501Z
⏰ UnifiedTimerManager: 1 个定时器到期

[440] [+15910ms] [LOG] 2025-07-18T21:12:39.501Z
⏰ UnifiedTimerManager: 1 个定时器到期

[441] [+15911ms] [LOG] 2025-07-18T21:12:39.502Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[442] [+15911ms] [LOG] 2025-07-18T21:12:39.502Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[443] [+15912ms] [LOG] 2025-07-18T21:12:39.503Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[444] [+15912ms] [LOG] 2025-07-18T21:12:39.503Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[445] [+15913ms] [LOG] 2025-07-18T21:12:39.504Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:40

[446] [+15913ms] [LOG] 2025-07-18T21:12:39.504Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:40

[447] [+17011ms] [LOG] 2025-07-18T21:12:40.602Z
⏰ UnifiedTimerManager: 1 个定时器到期

[448] [+17011ms] [LOG] 2025-07-18T21:12:40.602Z
⏰ UnifiedTimerManager: 1 个定时器到期

[449] [+17011ms] [LOG] 2025-07-18T21:12:40.602Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 101ms

[450] [+17011ms] [LOG] 2025-07-18T21:12:40.602Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 101ms

[451] [+17012ms] [LOG] 2025-07-18T21:12:40.603Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[452] [+17012ms] [LOG] 2025-07-18T21:12:40.603Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[453] [+17013ms] [LOG] 2025-07-18T21:12:40.604Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:41

[454] [+17013ms] [LOG] 2025-07-18T21:12:40.604Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:41

[455] [+18110ms] [LOG] 2025-07-18T21:12:41.701Z
⏰ UnifiedTimerManager: 1 个定时器到期

[456] [+18110ms] [LOG] 2025-07-18T21:12:41.701Z
⏰ UnifiedTimerManager: 1 个定时器到期

[457] [+18110ms] [LOG] 2025-07-18T21:12:41.701Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[458] [+18110ms] [LOG] 2025-07-18T21:12:41.701Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[459] [+18110ms] [LOG] 2025-07-18T21:12:41.701Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[460] [+18110ms] [LOG] 2025-07-18T21:12:41.701Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[461] [+18111ms] [LOG] 2025-07-18T21:12:41.702Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:42

[462] [+18111ms] [LOG] 2025-07-18T21:12:41.702Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:42

[463] [+19109ms] [LOG] 2025-07-18T21:12:42.700Z
⏰ UnifiedTimerManager: 1 个定时器到期

[464] [+19109ms] [LOG] 2025-07-18T21:12:42.700Z
⏰ UnifiedTimerManager: 1 个定时器到期

[465] [+19109ms] [LOG] 2025-07-18T21:12:42.700Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[466] [+19109ms] [LOG] 2025-07-18T21:12:42.700Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[467] [+19110ms] [LOG] 2025-07-18T21:12:42.701Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[468] [+19110ms] [LOG] 2025-07-18T21:12:42.701Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[469] [+19110ms] [LOG] 2025-07-18T21:12:42.701Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:43

[470] [+19110ms] [LOG] 2025-07-18T21:12:42.701Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:43

[471] [+20208ms] [LOG] 2025-07-18T21:12:43.799Z
⏰ UnifiedTimerManager: 1 个定时器到期

[472] [+20208ms] [LOG] 2025-07-18T21:12:43.799Z
⏰ UnifiedTimerManager: 1 个定时器到期

[473] [+20208ms] [LOG] 2025-07-18T21:12:43.799Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[474] [+20208ms] [LOG] 2025-07-18T21:12:43.799Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[475] [+20208ms] [LOG] 2025-07-18T21:12:43.799Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[476] [+20208ms] [LOG] 2025-07-18T21:12:43.799Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[477] [+20209ms] [LOG] 2025-07-18T21:12:43.800Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:44

[478] [+20209ms] [LOG] 2025-07-18T21:12:43.800Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:44

[479] [+21208ms] [LOG] 2025-07-18T21:12:44.799Z
⏰ UnifiedTimerManager: 1 个定时器到期

[480] [+21208ms] [LOG] 2025-07-18T21:12:44.799Z
⏰ UnifiedTimerManager: 1 个定时器到期

[481] [+21208ms] [LOG] 2025-07-18T21:12:44.799Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[482] [+21208ms] [LOG] 2025-07-18T21:12:44.799Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[483] [+21208ms] [LOG] 2025-07-18T21:12:44.799Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[484] [+21208ms] [LOG] 2025-07-18T21:12:44.799Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[485] [+21208ms] [LOG] 2025-07-18T21:12:44.799Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:45

[486] [+21208ms] [LOG] 2025-07-18T21:12:44.799Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:45

[487] [+22307ms] [LOG] 2025-07-18T21:12:45.898Z
⏰ UnifiedTimerManager: 1 个定时器到期

[488] [+22308ms] [LOG] 2025-07-18T21:12:45.899Z
⏰ UnifiedTimerManager: 1 个定时器到期

[489] [+22308ms] [LOG] 2025-07-18T21:12:45.899Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[490] [+22308ms] [LOG] 2025-07-18T21:12:45.899Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[491] [+22308ms] [LOG] 2025-07-18T21:12:45.899Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[492] [+22308ms] [LOG] 2025-07-18T21:12:45.899Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[493] [+22308ms] [LOG] 2025-07-18T21:12:45.899Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:46

[494] [+22308ms] [LOG] 2025-07-18T21:12:45.899Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:46

[495] [+23307ms] [LOG] 2025-07-18T21:12:46.898Z
⏰ UnifiedTimerManager: 1 个定时器到期

[496] [+23307ms] [LOG] 2025-07-18T21:12:46.898Z
⏰ UnifiedTimerManager: 1 个定时器到期

[497] [+23307ms] [LOG] 2025-07-18T21:12:46.898Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[498] [+23307ms] [LOG] 2025-07-18T21:12:46.898Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[499] [+23307ms] [LOG] 2025-07-18T21:12:46.898Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[500] [+23307ms] [LOG] 2025-07-18T21:12:46.898Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[501] [+23307ms] [LOG] 2025-07-18T21:12:46.898Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:47

[502] [+23307ms] [LOG] 2025-07-18T21:12:46.898Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:47

[503] [+24404ms] [LOG] 2025-07-18T21:12:47.995Z
⏰ UnifiedTimerManager: 1 个定时器到期

[504] [+24404ms] [LOG] 2025-07-18T21:12:47.995Z
⏰ UnifiedTimerManager: 1 个定时器到期

[505] [+24405ms] [LOG] 2025-07-18T21:12:47.996Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[506] [+24405ms] [LOG] 2025-07-18T21:12:47.996Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[507] [+24405ms] [LOG] 2025-07-18T21:12:47.996Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[508] [+24405ms] [LOG] 2025-07-18T21:12:47.996Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[509] [+24405ms] [LOG] 2025-07-18T21:12:47.996Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:48

[510] [+24405ms] [LOG] 2025-07-18T21:12:47.996Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:48

[511] [+25406ms] [LOG] 2025-07-18T21:12:48.997Z
⏰ UnifiedTimerManager: 1 个定时器到期

[512] [+25406ms] [LOG] 2025-07-18T21:12:48.997Z
⏰ UnifiedTimerManager: 1 个定时器到期

[513] [+25407ms] [LOG] 2025-07-18T21:12:48.998Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[514] [+25407ms] [LOG] 2025-07-18T21:12:48.998Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[515] [+25407ms] [LOG] 2025-07-18T21:12:48.998Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[516] [+25407ms] [LOG] 2025-07-18T21:12:48.998Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[517] [+25407ms] [LOG] 2025-07-18T21:12:48.998Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:49

[518] [+25407ms] [LOG] 2025-07-18T21:12:48.998Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:49

[519] [+26406ms] [LOG] 2025-07-18T21:12:49.997Z
⏰ UnifiedTimerManager: 1 个定时器到期

[520] [+26406ms] [LOG] 2025-07-18T21:12:49.997Z
⏰ UnifiedTimerManager: 1 个定时器到期

[521] [+26406ms] [LOG] 2025-07-18T21:12:49.997Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[522] [+26406ms] [LOG] 2025-07-18T21:12:49.997Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[523] [+26406ms] [LOG] 2025-07-18T21:12:49.997Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[524] [+26406ms] [LOG] 2025-07-18T21:12:49.997Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[525] [+26406ms] [LOG] 2025-07-18T21:12:49.997Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:50

[526] [+26406ms] [LOG] 2025-07-18T21:12:49.997Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:50

[527] [+27406ms] [LOG] 2025-07-18T21:12:50.997Z
⏰ UnifiedTimerManager: 1 个定时器到期

[528] [+27406ms] [LOG] 2025-07-18T21:12:50.997Z
⏰ UnifiedTimerManager: 1 个定时器到期

[529] [+27407ms] [LOG] 2025-07-18T21:12:50.998Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[530] [+27407ms] [LOG] 2025-07-18T21:12:50.998Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[531] [+27407ms] [LOG] 2025-07-18T21:12:50.998Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[532] [+27407ms] [LOG] 2025-07-18T21:12:50.998Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[533] [+27408ms] [LOG] 2025-07-18T21:12:50.999Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:51

[534] [+27408ms] [LOG] 2025-07-18T21:12:50.999Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:51

[535] [+28505ms] [LOG] 2025-07-18T21:12:52.096Z
⏰ UnifiedTimerManager: 1 个定时器到期

[536] [+28505ms] [LOG] 2025-07-18T21:12:52.096Z
⏰ UnifiedTimerManager: 1 个定时器到期

[537] [+28505ms] [LOG] 2025-07-18T21:12:52.096Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[538] [+28505ms] [LOG] 2025-07-18T21:12:52.096Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[539] [+28505ms] [LOG] 2025-07-18T21:12:52.096Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[540] [+28505ms] [LOG] 2025-07-18T21:12:52.096Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[541] [+28505ms] [LOG] 2025-07-18T21:12:52.096Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:53

[542] [+28505ms] [LOG] 2025-07-18T21:12:52.096Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:53

[543] [+29506ms] [LOG] 2025-07-18T21:12:53.097Z
⏰ UnifiedTimerManager: 1 个定时器到期

[544] [+29506ms] [LOG] 2025-07-18T21:12:53.097Z
⏰ UnifiedTimerManager: 1 个定时器到期

[545] [+29506ms] [LOG] 2025-07-18T21:12:53.097Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[546] [+29506ms] [LOG] 2025-07-18T21:12:53.097Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[547] [+29507ms] [LOG] 2025-07-18T21:12:53.098Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[548] [+29507ms] [LOG] 2025-07-18T21:12:53.098Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[549] [+29507ms] [LOG] 2025-07-18T21:12:53.098Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:54

[550] [+29507ms] [LOG] 2025-07-18T21:12:53.098Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:54

[551] [+30604ms] [LOG] 2025-07-18T21:12:54.195Z
⏰ UnifiedTimerManager: 1 个定时器到期

[552] [+30604ms] [LOG] 2025-07-18T21:12:54.195Z
⏰ UnifiedTimerManager: 1 个定时器到期

[553] [+30604ms] [LOG] 2025-07-18T21:12:54.195Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[554] [+30604ms] [LOG] 2025-07-18T21:12:54.195Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[555] [+30604ms] [LOG] 2025-07-18T21:12:54.195Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[556] [+30604ms] [LOG] 2025-07-18T21:12:54.195Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[557] [+30604ms] [LOG] 2025-07-18T21:12:54.195Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:55

[558] [+30604ms] [LOG] 2025-07-18T21:12:54.195Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:55

[559] [+31603ms] [LOG] 2025-07-18T21:12:55.194Z
⏰ UnifiedTimerManager: 1 个定时器到期

[560] [+31603ms] [LOG] 2025-07-18T21:12:55.194Z
⏰ UnifiedTimerManager: 1 个定时器到期

[561] [+31603ms] [LOG] 2025-07-18T21:12:55.194Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[562] [+31604ms] [LOG] 2025-07-18T21:12:55.195Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[563] [+31604ms] [LOG] 2025-07-18T21:12:55.195Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[564] [+31604ms] [LOG] 2025-07-18T21:12:55.195Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[565] [+31604ms] [LOG] 2025-07-18T21:12:55.195Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:56

[566] [+31604ms] [LOG] 2025-07-18T21:12:55.195Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:56

[567] [+32610ms] [LOG] 2025-07-18T21:12:56.201Z
⏰ UnifiedTimerManager: 1 个定时器到期

[568] [+32610ms] [LOG] 2025-07-18T21:12:56.201Z
⏰ UnifiedTimerManager: 1 个定时器到期

[569] [+32610ms] [LOG] 2025-07-18T21:12:56.201Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 7ms

[570] [+32610ms] [LOG] 2025-07-18T21:12:56.201Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 7ms

[571] [+32610ms] [LOG] 2025-07-18T21:12:56.201Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[572] [+32611ms] [LOG] 2025-07-18T21:12:56.202Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[573] [+32611ms] [LOG] 2025-07-18T21:12:56.202Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:57

[574] [+32611ms] [LOG] 2025-07-18T21:12:56.202Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:57

[575] [+33618ms] [LOG] 2025-07-18T21:12:57.209Z
⏰ UnifiedTimerManager: 1 个定时器到期

[576] [+33618ms] [LOG] 2025-07-18T21:12:57.209Z
⏰ UnifiedTimerManager: 1 个定时器到期

[577] [+33618ms] [LOG] 2025-07-18T21:12:57.209Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 7ms

[578] [+33618ms] [LOG] 2025-07-18T21:12:57.209Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 7ms

[579] [+33618ms] [LOG] 2025-07-18T21:12:57.209Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[580] [+33618ms] [LOG] 2025-07-18T21:12:57.209Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[581] [+33618ms] [LOG] 2025-07-18T21:12:57.209Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:58

[582] [+33618ms] [LOG] 2025-07-18T21:12:57.209Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:58

[583] [+34705ms] [LOG] 2025-07-18T21:12:58.296Z
⏰ UnifiedTimerManager: 1 个定时器到期

[584] [+34705ms] [LOG] 2025-07-18T21:12:58.296Z
⏰ UnifiedTimerManager: 1 个定时器到期

[585] [+34705ms] [LOG] 2025-07-18T21:12:58.296Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 88ms

[586] [+34705ms] [LOG] 2025-07-18T21:12:58.296Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 88ms

[587] [+34706ms] [LOG] 2025-07-18T21:12:58.297Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[588] [+34706ms] [LOG] 2025-07-18T21:12:58.297Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[589] [+34706ms] [LOG] 2025-07-18T21:12:58.297Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:59

[590] [+34706ms] [LOG] 2025-07-18T21:12:58.297Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:12:59

[591] [+35716ms] [LOG] 2025-07-18T21:12:59.307Z
⏰ UnifiedTimerManager: 1 个定时器到期

[592] [+35716ms] [LOG] 2025-07-18T21:12:59.307Z
⏰ UnifiedTimerManager: 1 个定时器到期

[593] [+35716ms] [LOG] 2025-07-18T21:12:59.307Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 11ms

[594] [+35716ms] [LOG] 2025-07-18T21:12:59.307Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 11ms

[595] [+35717ms] [LOG] 2025-07-18T21:12:59.308Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[596] [+35717ms] [LOG] 2025-07-18T21:12:59.308Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[597] [+35717ms] [LOG] 2025-07-18T21:12:59.308Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:00

[598] [+35717ms] [LOG] 2025-07-18T21:12:59.308Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:00

[599] [+36808ms] [LOG] 2025-07-18T21:13:00.399Z
⏰ UnifiedTimerManager: 1 个定时器到期

[600] [+36808ms] [LOG] 2025-07-18T21:13:00.399Z
⏰ UnifiedTimerManager: 1 个定时器到期

[601] [+36808ms] [LOG] 2025-07-18T21:13:00.399Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[602] [+36808ms] [LOG] 2025-07-18T21:13:00.399Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[603] [+36809ms] [LOG] 2025-07-18T21:13:00.400Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[604] [+36809ms] [LOG] 2025-07-18T21:13:00.400Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[605] [+36809ms] [LOG] 2025-07-18T21:13:00.400Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:01

[606] [+36809ms] [LOG] 2025-07-18T21:13:00.400Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:01

[607] [+37819ms] [LOG] 2025-07-18T21:13:01.410Z
⏰ UnifiedTimerManager: 1 个定时器到期

[608] [+37819ms] [LOG] 2025-07-18T21:13:01.410Z
⏰ UnifiedTimerManager: 1 个定时器到期

[609] [+37820ms] [LOG] 2025-07-18T21:13:01.411Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 10ms

[610] [+37820ms] [LOG] 2025-07-18T21:13:01.411Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 10ms

[611] [+37820ms] [LOG] 2025-07-18T21:13:01.411Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[612] [+37820ms] [LOG] 2025-07-18T21:13:01.411Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[613] [+37821ms] [LOG] 2025-07-18T21:13:01.412Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:02

[614] [+37821ms] [LOG] 2025-07-18T21:13:01.412Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:02

[615] [+38908ms] [LOG] 2025-07-18T21:13:02.499Z
⏰ UnifiedTimerManager: 1 个定时器到期

[616] [+38908ms] [LOG] 2025-07-18T21:13:02.499Z
⏰ UnifiedTimerManager: 1 个定时器到期

[617] [+38909ms] [LOG] 2025-07-18T21:13:02.500Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 90ms

[618] [+38909ms] [LOG] 2025-07-18T21:13:02.500Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 90ms

[619] [+38909ms] [LOG] 2025-07-18T21:13:02.500Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[620] [+38909ms] [LOG] 2025-07-18T21:13:02.500Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[621] [+38909ms] [LOG] 2025-07-18T21:13:02.500Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:03

[622] [+38909ms] [LOG] 2025-07-18T21:13:02.500Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:03

[623] [+39916ms] [LOG] 2025-07-18T21:13:03.507Z
⏰ UnifiedTimerManager: 1 个定时器到期

[624] [+39916ms] [LOG] 2025-07-18T21:13:03.507Z
⏰ UnifiedTimerManager: 1 个定时器到期

[625] [+39916ms] [LOG] 2025-07-18T21:13:03.507Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 8ms

[626] [+39916ms] [LOG] 2025-07-18T21:13:03.507Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 8ms

[627] [+39916ms] [LOG] 2025-07-18T21:13:03.507Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[628] [+39916ms] [LOG] 2025-07-18T21:13:03.507Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[629] [+39917ms] [LOG] 2025-07-18T21:13:03.508Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:04

[630] [+39917ms] [LOG] 2025-07-18T21:13:03.508Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:04

[631] [+41008ms] [LOG] 2025-07-18T21:13:04.599Z
⏰ UnifiedTimerManager: 1 个定时器到期

[632] [+41008ms] [LOG] 2025-07-18T21:13:04.599Z
⏰ UnifiedTimerManager: 1 个定时器到期

[633] [+41008ms] [LOG] 2025-07-18T21:13:04.599Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[634] [+41008ms] [LOG] 2025-07-18T21:13:04.599Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[635] [+41008ms] [LOG] 2025-07-18T21:13:04.599Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[636] [+41008ms] [LOG] 2025-07-18T21:13:04.599Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[637] [+41008ms] [LOG] 2025-07-18T21:13:04.599Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:05

[638] [+41008ms] [LOG] 2025-07-18T21:13:04.599Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:05

[639] [+42114ms] [LOG] 2025-07-18T21:13:05.705Z
⏰ UnifiedTimerManager: 1 个定时器到期

[640] [+42114ms] [LOG] 2025-07-18T21:13:05.705Z
⏰ UnifiedTimerManager: 1 个定时器到期

[641] [+42115ms] [LOG] 2025-07-18T21:13:05.706Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 106ms

[642] [+42115ms] [LOG] 2025-07-18T21:13:05.706Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 106ms

[643] [+42116ms] [LOG] 2025-07-18T21:13:05.707Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[644] [+42116ms] [LOG] 2025-07-18T21:13:05.707Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[645] [+42116ms] [LOG] 2025-07-18T21:13:05.707Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:06

[646] [+42116ms] [LOG] 2025-07-18T21:13:05.707Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:06

[647] [+43215ms] [LOG] 2025-07-18T21:13:06.806Z
⏰ UnifiedTimerManager: 1 个定时器到期

[648] [+43215ms] [LOG] 2025-07-18T21:13:06.806Z
⏰ UnifiedTimerManager: 1 个定时器到期

[649] [+43215ms] [LOG] 2025-07-18T21:13:06.806Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[650] [+43215ms] [LOG] 2025-07-18T21:13:06.806Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[651] [+43215ms] [LOG] 2025-07-18T21:13:06.806Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[652] [+43215ms] [LOG] 2025-07-18T21:13:06.806Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[653] [+43215ms] [LOG] 2025-07-18T21:13:06.806Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:07

[654] [+43215ms] [LOG] 2025-07-18T21:13:06.806Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:07

[655] [+44313ms] [LOG] 2025-07-18T21:13:07.904Z
⏰ UnifiedTimerManager: 1 个定时器到期

[656] [+44313ms] [LOG] 2025-07-18T21:13:07.904Z
⏰ UnifiedTimerManager: 1 个定时器到期

[657] [+44313ms] [LOG] 2025-07-18T21:13:07.904Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[658] [+44313ms] [LOG] 2025-07-18T21:13:07.904Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[659] [+44313ms] [LOG] 2025-07-18T21:13:07.904Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[660] [+44313ms] [LOG] 2025-07-18T21:13:07.904Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[661] [+44313ms] [LOG] 2025-07-18T21:13:07.904Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:08

[662] [+44313ms] [LOG] 2025-07-18T21:13:07.904Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:08

[663] [+45312ms] [LOG] 2025-07-18T21:13:08.903Z
⏰ UnifiedTimerManager: 1 个定时器到期

[664] [+45312ms] [LOG] 2025-07-18T21:13:08.903Z
⏰ UnifiedTimerManager: 1 个定时器到期

[665] [+45313ms] [LOG] 2025-07-18T21:13:08.904Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[666] [+45313ms] [LOG] 2025-07-18T21:13:08.904Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[667] [+45313ms] [LOG] 2025-07-18T21:13:08.904Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[668] [+45313ms] [LOG] 2025-07-18T21:13:08.904Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[669] [+45313ms] [LOG] 2025-07-18T21:13:08.904Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:09

[670] [+45313ms] [LOG] 2025-07-18T21:13:08.904Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:09

[671] [+46406ms] [LOG] 2025-07-18T21:13:09.997Z
⏰ UnifiedTimerManager: 1 个定时器到期

[672] [+46406ms] [LOG] 2025-07-18T21:13:09.997Z
⏰ UnifiedTimerManager: 1 个定时器到期

[673] [+46407ms] [LOG] 2025-07-18T21:13:09.998Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[674] [+46407ms] [LOG] 2025-07-18T21:13:09.998Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[675] [+46407ms] [LOG] 2025-07-18T21:13:09.998Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[676] [+46407ms] [LOG] 2025-07-18T21:13:09.998Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[677] [+46407ms] [LOG] 2025-07-18T21:13:09.998Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:10

[678] [+46407ms] [LOG] 2025-07-18T21:13:09.998Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 05:13:10

[679] [+47081ms] [LOG] 2025-07-18T21:13:10.672Z
📨 WebSocket消息: {
  "type": "ping",
  "timestamp": 123456
}
  参数2: {
  "type": "ping",
  "timestamp": 123456
}

[680] [+47081ms] [LOG] 2025-07-18T21:13:10.672Z
📨 WebSocket消息: {
  "type": "ping",
  "timestamp": 123456
}
  参数2: {
  "type": "ping",
  "timestamp": 123456
}

[681] [+47081ms] [LOG] 2025-07-18T21:13:10.672Z
🏓 回复pong消息

[682] [+47081ms] [LOG] 2025-07-18T21:13:10.672Z
🏓 回复pong消息

