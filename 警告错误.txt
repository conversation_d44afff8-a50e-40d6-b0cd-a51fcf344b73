In file included from src/core/timer_manager_backend.h:6,
                 from src/core/timer_manager_backend.cpp:1:
src/core/../storage/timer_data.h: In static member function 'static TimerTask TimerTask::fromJson(const ArduinoJson::V742NB22::JsonDocument&)':
src/core/../storage/timer_data.h:101:68: error: invalid use of incomplete type 'class ArduinoJson::V742NB22::detail::InvalidConversion<ArduinoJson::V742NB22::JsonVariantConst, ArduinoJson::V742NB22::JsonArray>'
             JsonArray signalArray = doc["signalIds"].as<JsonArray>();
                                                                    ^
In file included from .pio/libdeps/esp32-s3-devkitc-1/ArduinoJson/src/ArduinoJson/Variant/VariantRefBase.hpp:8,
                 from .pio/libdeps/esp32-s3-devkitc-1/ArduinoJson/src/ArduinoJson/Array/ElementProxy.hpp:7,
                 from .pio/libdeps/esp32-s3-devkitc-1/ArduinoJson/src/ArduinoJson/Array/JsonArray.hpp:7,
                 from .pio/libdeps/esp32-s3-devkitc-1/ArduinoJson/src/ArduinoJson.hpp:38,
                 from .pio/libdeps/esp32-s3-devkitc-1/ArduinoJson/src/ArduinoJson.h:9,
                 from src/core/../storage/timer_data.h:4,
                 from src/core/timer_manager_backend.h:6,
                 from src/core/timer_manager_backend.cpp:1:
.pio/libdeps/esp32-s3-devkitc-1/ArduinoJson/src/ArduinoJson/Variant/Converter.hpp:20:7: note: declaration of 'class ArduinoJson::V742NB22::detail::InvalidConversion<ArduinoJson::V742NB22::JsonVariantConst, ArduinoJson::V742NB22::JsonArray>'
 class InvalidConversion;  // Error here? See https://arduinojson.org/v7/invalid-conversion/       
       ^~~~~~~~~~~~~~~~~
src/core/timer_manager_backend.cpp: In member function 'bool TimerManagerBackend::emitSingleSignal(const String&)':
src/core/timer_manager_backend.cpp:453:59: error: cannot convert 'SignalData' to 'SignalData*' in initialization
     SignalData* signal = signalStorage->getSignal(signalId);
                                                           ^
src/core/timer_manager_backend.cpp:459:31: error: 'class IRSignalManager' has no member named 'sendSignal'; did you mean 'emitSignal'?
     bool success = irManager->sendSignal(*signal);
                               ^~~~~~~~~~
                               emitSignal
src/core/timer_manager_backend.cpp:464:54: error: no matching function for call to 'SignalStorage::updateSignal(const String&, SignalData&)'
         signalStorage->updateSignal(signalId, *signal);
                                                      ^
In file included from src/core/timer_manager_backend.h:7,
                 from src/core/timer_manager_backend.cpp:1:
src/core/../storage/signal_storage.h:108:10: note: candidate: 'bool SignalStorage::updateSignal(const SignalData&)'
     bool updateSignal(const SignalData& signal);
          ^~~~~~~~~~~~
src/core/../storage/signal_storage.h:108:10: note:   candidate expects 1 argument, 2 provided      
*** [.pio\build\esp32-s3-devkitc-1\src\core\timer_manager_backend.cpp.o] Error 1
In file included from src/core/timer_manager_backend.h:6,
                 from src/core/system_manager.cpp:8:
src/core/../storage/timer_data.h: In static member function 'static TimerTask TimerTask::fromJson(const ArduinoJson::V742NB22::JsonDocument&)':
src/core/../storage/timer_data.h:101:68: error: invalid use of incomplete type 'class ArduinoJson::V742NB22::detail::InvalidConversion<ArduinoJson::V742NB22::JsonVariantConst, ArduinoJson::V742NB22::JsonArray>'
             JsonArray signalArray = doc["signalIds"].as<JsonArray>();
                                                                    ^
In file included from .pio/libdeps/esp32-s3-devkitc-1/ArduinoJson/src/ArduinoJson/Variant/VariantRefBase.hpp:8,
                 from .pio/libdeps/esp32-s3-devkitc-1/ArduinoJson/src/ArduinoJson/Array/ElementProxy.hpp:7,
                 from .pio/libdeps/esp32-s3-devkitc-1/ArduinoJson/src/ArduinoJson/Array/JsonArray.hpp:7,
                 from .pio/libdeps/esp32-s3-devkitc-1/ArduinoJson/src/ArduinoJson.hpp:38,
                 from .pio/libdeps/esp32-s3-devkitc-1/ArduinoJson/src/ArduinoJson.h:9,
                 from src/core/system_manager.h:15,
                 from src/core/system_manager.cpp:1:
.pio/libdeps/esp32-s3-devkitc-1/ArduinoJson/src/ArduinoJson/Variant/Converter.hpp:20:7: note: declaration of 'class ArduinoJson::V742NB22::detail::InvalidConversion<ArduinoJson::V742NB22::JsonVariantConst, ArduinoJson::V742NB22::JsonArray>'
 class InvalidConversion;  // Error here? See https://arduinojson.org/v7/invalid-conversion/       
       ^~~~~~~~~~~~~~~~~
*** [.pio\build\esp32-s3-devkitc-1\src\core\system_manager.cpp.o] Error 1