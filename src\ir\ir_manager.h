#ifndef IR_MANAGER_H
#define IR_MANAGER_H

/**
 * @file ir_manager.h
 * @brief 红外信号管理器 - 红外信号学习和发射
 * @details 管理红外信号的学习、发射、协议识别等功能
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>
#include <IRremoteESP8266.h>
#include <IRsend.h>
#include <IRrecv.h>
#include <IRutils.h>
#include <ArduinoJson.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/semphr.h>

#include "config/config.h"
#include "config/pins.h"
#include "config/constants.h"
#include "storage/data_structures.h"

/**
 * @brief 红外信号管理器类
 * @details 处理红外信号的学习、发射和协议识别
 */
class IRSignalManager {
private:
    // 红外组件
    IRsend* irSend;
    IRrecv* irRecv;
    decode_results results;
    
    // 学习状态枚举
    enum LearningState {
        IDLE = 0,
        WAITING_FOR_SIGNAL = 1,
        PROCESSING_SIGNAL = 2,
        LEARNING_COMPLETE = 3,
        LEARNING_ERROR = 4,
        LEARNING_TIMEOUT = 5
    };
    
    // 发射状态枚举
    enum EmitState {
        EMIT_IDLE = 0,
        EMIT_PREPARING = 1,
        EMIT_SENDING = 2,
        EMIT_COMPLETE = 3,
        EMIT_ERROR = 4
    };
    
    // 状态管理
    LearningState learningState;
    EmitState emitState;
    uint32_t learningStartTime;
    uint32_t learningTimeout;
    uint32_t lastEmitTime;
    
    // 学习配置
    bool isLearningActive;
    String currentLearningId;
    JsonDocument learnedSignalData;
    
    // 发射配置
    uint32_t defaultFrequency;
    uint8_t defaultDutyCycle;
    uint16_t emitRepeatCount;

    // 引脚配置
    uint8_t irSendPin;
    uint8_t irRecvPin;
    
    // 同步原语
    SemaphoreHandle_t irMutex;
    TaskHandle_t learningTaskHandle;
    
    // 统计信息
    uint32_t totalSignalsLearned;
    uint32_t totalSignalsEmitted;
    uint32_t learningAttempts;
    uint32_t emitAttempts;

public:
    /**
     * @brief 构造函数
     */
    IRSignalManager();
    
    /**
     * @brief 析构函数
     */
    ~IRSignalManager();
    
    // ==================== 生命周期管理 ====================
    
    /**
     * @brief 初始化红外管理器（使用默认引脚）
     * @return bool 初始化是否成功
     */
    bool begin();

    /**
     * @brief 初始化红外管理器（使用自定义引脚）
     * @param sendPin 红外发射引脚
     * @param recvPin 红外接收引脚
     * @param frequency 红外频率
     * @param dutyCycle 占空比
     * @return bool 初始化是否成功
     */
    bool begin(uint8_t sendPin, uint8_t recvPin, uint32_t frequency = IR_FREQUENCY, uint8_t dutyCycle = IR_DUTY_CYCLE);
    
    /**
     * @brief 红外管理器主循环
     */
    void loop();
    
    /**
     * @brief 停止红外管理器
     */
    void stop();
    
    // ==================== 信号学习 ====================
    
    /**
     * @brief 开始信号学习
     * @param timeout 学习超时时间(ms)
     * @param signalId 信号ID (可选)
     * @return bool 启动是否成功
     */
    bool startLearning(uint32_t timeout = LEARNING_TIMEOUT, const String& signalId = "");
    
    /**
     * @brief 停止信号学习
     * @return bool 停止是否成功
     */
    bool stopLearning();
    
    /**
     * @brief 检查是否正在学习
     * @return bool 是否正在学习
     */
    bool isLearning() const { return isLearningActive; }
    
    /**
     * @brief 获取学习状态
     * @return JsonDocument 学习状态信息
     */
    JsonDocument getLearningStatus() const;
    
    // ==================== 信号发射 ====================
    
    /**
     * @brief 发射信号
     * @param signalId 信号ID
     * @return bool 发射是否成功
     */
    bool emitSignal(const String& signalId);
    
    /**
     * @brief 发射原始信号数据
     * @param signalData 信号数据
     * @return bool 发射是否成功
     */
    bool emitRawSignal(const SignalData& signalData);
    
    /**
     * @brief 发射指定协议的信号
     * @param protocol 协议类型
     * @param data 信号数据
     * @param bits 数据位数
     * @param frequency 载波频率
     * @return bool 发射是否成功
     */
    bool emitProtocolSignal(const String& protocol, uint64_t data, uint16_t bits, uint32_t frequency = 38000);
    
    /**
     * @brief 重复发射信号
     * @param signalId 信号ID
     * @param repeatCount 重复次数
     * @param interval 间隔时间(ms)
     * @return bool 发射是否成功
     */
    bool repeatEmitSignal(const String& signalId, uint16_t repeatCount, uint32_t interval = 500);
    
    // ==================== 协议处理 ====================
    
    /**
     * @brief 识别信号协议
     * @param rawData 原始数据
     * @return String 协议名称
     */
    String identifyProtocol(const String& rawData);
    
    /**
     * @brief 验证信号数据有效性
     * @param signalData 信号数据
     * @return bool 是否有效
     */
    bool validateSignalData(const SignalData& signalData);
    
    /**
     * @brief 获取支持的协议列表
     * @return std::vector<String> 协议列表
     */
    std::vector<String> getSupportedProtocols() const;
    
    // ==================== 配置管理 ====================
    
    /**
     * @brief 设置默认载波频率
     * @param frequency 频率(Hz)
     */
    void setDefaultFrequency(uint32_t frequency) { defaultFrequency = frequency; }
    
    /**
     * @brief 设置默认占空比
     * @param dutyCycle 占空比(%)
     */
    void setDefaultDutyCycle(uint8_t dutyCycle) { defaultDutyCycle = dutyCycle; }
    
    /**
     * @brief 设置发射重复次数
     * @param count 重复次数
     */
    void setEmitRepeatCount(uint16_t count) { emitRepeatCount = count; }
    
    /**
     * @brief 设置学习超时时间
     * @param timeout 超时时间(ms)
     */
    void setLearningTimeout(uint32_t timeout) { learningTimeout = timeout; }
    
    // ==================== 状态查询 ====================
    
    /**
     * @brief 获取红外管理器状态
     * @return JsonDocument 状态信息
     */
    JsonDocument getIRStatus() const;
    
    /**
     * @brief 获取统计信息
     * @return JsonDocument 统计信息
     */
    JsonDocument getStatistics() const;
    
    /**
     * @brief 检查硬件状态
     * @return bool 硬件是否正常
     */
    bool checkHardwareStatus() const;

private:
    // ==================== 私有方法 ====================
    
    /**
     * @brief 处理接收到的信号
     */
    void processReceivedSignal();
    
    /**
     * @brief 学习任务函数
     * @param parameter 任务参数
     */
    static void learningTask(void* parameter);
    
    /**
     * @brief 学习处理循环
     */
    void processLearning();
    
    /**
     * @brief 生成信号ID
     * @return String 唯一信号ID
     */
    String generateSignalId();
    
    /**
     * @brief 协议类型转字符串
     * @param protocol 协议类型
     * @return String 协议名称
     */
    String protocolToString(decode_type_t protocol) const;
    
    /**
     * @brief 字符串转协议类型
     * @param protocolStr 协议名称
     * @return decode_type_t 协议类型
     */
    decode_type_t stringToProtocol(const String& protocolStr) const;
    
    /**
     * @brief 64位整数转十六进制字符串
     * @param value 数值
     * @param base 进制
     * @return String 字符串表示
     */
    String uint64ToString(uint64_t value, uint8_t base = 16) const;
    
    /**
     * @brief 十六进制字符串转64位整数
     * @param str 字符串
     * @return uint64_t 数值
     */
    uint64_t stringToUint64(const String& str) const;
    
    /**
     * @brief 发送学习完成事件
     * @param signalData 信号数据
     */
    void sendLearningCompleteEvent(const JsonDocument& signalData);
    
    /**
     * @brief 发送发射完成事件
     * @param signalId 信号ID
     * @param success 是否成功
     */
    void sendEmitCompleteEvent(const String& signalId, bool success);
    
    /**
     * @brief 记录红外日志
     * @param level 日志级别
     * @param message 日志消息
     */
    void logIR(const String& level, const String& message);
};

#endif // IR_MANAGER_H
